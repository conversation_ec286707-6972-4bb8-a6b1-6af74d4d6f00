/**
 * 随机邮箱生成器插件
 * 使用方法：
 * 1. 引入此JS文件
 * 2. 调用 EmailGenerator.init() 初始化
 * 3. 调用 EmailGenerator.generate() 生成邮箱
 */

const EmailGenerator = {
    // 预定义的用户名前缀
    prefixes: [
        'user', 'admin', 'test', 'demo', 'temp', 'guest', 'member', 'client',
        'student', 'teacher', 'manager', 'developer', 'designer', 'writer',
        'editor', 'reviewer', 'analyst', 'consultant', 'expert', 'specialist',
        'assistant', 'helper', 'support', 'service', 'contact', 'info',
        'hello', 'welcome', 'new', 'active', 'premium', 'vip', 'pro',
        'beta', 'alpha', 'trial', 'free', 'basic', 'standard', 'advanced',
        'cool', 'smart', 'quick', 'fast', 'super', 'mega', 'ultra', 'max',
        'mini', 'micro', 'nano', 'big', 'small', 'tiny', 'huge', 'giant'
    ],
    
    // 当前生成的邮箱
    currentEmail: '',
    
    // 生成计数
    generateCount: 0,
    
    // 初始化插件
    init: function() {
        // 从本地存储恢复计数
        const savedCount = localStorage.getItem('emailGenerateCount');
        if (savedCount) {
            this.generateCount = parseInt(savedCount);
        }
        console.log('邮箱生成器插件已初始化');
    },
    
    // 生成随机字符串
    generateRandomString: function(length) {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },
    
    // 生成随机邮箱
    generate: function() {
        const prefix = this.prefixes[Math.floor(Math.random() * this.prefixes.length)];
        const randomPart = this.generateRandomString(Math.floor(Math.random() * 8) + 3);
        const separators = ['', '.', '_'];
        const separator = separators[Math.floor(Math.random() * separators.length)];
        
        this.currentEmail = prefix + separator + randomPart + '@shusj.xyz';
        this.generateCount++;
        
        // 保存到本地存储
        localStorage.setItem('emailGenerateCount', this.generateCount);
        
        return this.currentEmail;
    },
    
    // 获取当前邮箱
    getCurrentEmail: function() {
        return this.currentEmail;
    },
    
    // 获取生成计数
    getGenerateCount: function() {
        return this.generateCount;
    },
    
    // 复制邮箱到剪贴板
    copyToClipboard: function(email = null) {
        const emailToCopy = email || this.currentEmail;
        if (!emailToCopy) {
            console.warn('没有邮箱可复制');
            return false;
        }
        
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(emailToCopy).then(() => {
                console.log('邮箱已复制到剪贴板:', emailToCopy);
                return true;
            }).catch(err => {
                console.error('复制失败:', err);
                return this.fallbackCopy(emailToCopy);
            });
        } else {
            return this.fallbackCopy(emailToCopy);
        }
    },
    
    // 降级复制方案
    fallbackCopy: function(text) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            if (successful) {
                console.log('邮箱已复制到剪贴板:', text);
                return true;
            } else {
                console.error('复制失败');
                return false;
            }
        } catch (err) {
            console.error('复制失败:', err);
            return false;
        }
    },
    
    // 批量生成邮箱
    generateBatch: function(count = 5) {
        const emails = [];
        for (let i = 0; i < count; i++) {
            emails.push(this.generate());
        }
        return emails;
    },
    
    // 重置计数
    resetCount: function() {
        this.generateCount = 0;
        localStorage.removeItem('emailGenerateCount');
        console.log('生成计数已重置');
    },
    
    // 创建简单的UI组件
    createWidget: function(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器元素:', containerId);
            return;
        }
        
        container.innerHTML = `
            <div style="
                padding: 20px;
                border: 2px solid #667eea;
                border-radius: 10px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                text-align: center;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                max-width: 400px;
                margin: 0 auto;
            ">
                <h3 style="color: #333; margin-bottom: 15px;">🎲 随机邮箱生成器</h3>
                <div id="emailDisplay" style="
                    background: white;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 15px 0;
                    font-family: 'Courier New', monospace;
                    color: #495057;
                    border: 1px solid #dee2e6;
                    word-break: break-all;
                ">点击生成邮箱</div>
                <button id="generateBtn" style="
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    margin: 5px;
                    font-size: 14px;
                ">生成邮箱</button>
                <button id="copyBtn" style="
                    background: linear-gradient(45deg, #28a745, #20c997);
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    margin: 5px;
                    font-size: 14px;
                    opacity: 0.5;
                ">复制邮箱</button>
                <div style="margin-top: 15px; color: #6c757d; font-size: 12px;">
                    已生成: <span id="countDisplay">${this.generateCount}</span> 个邮箱
                </div>
            </div>
        `;
        
        // 绑定事件
        const generateBtn = container.querySelector('#generateBtn');
        const copyBtn = container.querySelector('#copyBtn');
        const emailDisplay = container.querySelector('#emailDisplay');
        const countDisplay = container.querySelector('#countDisplay');
        
        generateBtn.addEventListener('click', () => {
            const email = this.generate();
            emailDisplay.textContent = email;
            copyBtn.style.opacity = '1';
            countDisplay.textContent = this.generateCount;
        });
        
        copyBtn.addEventListener('click', () => {
            if (this.currentEmail) {
                this.copyToClipboard().then(success => {
                    if (success) {
                        copyBtn.textContent = '已复制!';
                        setTimeout(() => {
                            copyBtn.textContent = '复制邮箱';
                        }, 1000);
                    }
                });
            }
        });
    }
};

// 自动初始化
if (typeof window !== 'undefined') {
    EmailGenerator.init();
    
    // 如果页面已加载完成，立即执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('邮箱生成器插件已准备就绪');
        });
    } else {
        console.log('邮箱生成器插件已准备就绪');
    }
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.EmailGenerator = EmailGenerator;
}

// 支持模块化导入
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmailGenerator;
}
